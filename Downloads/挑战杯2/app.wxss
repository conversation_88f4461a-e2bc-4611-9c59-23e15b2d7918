/**app.wxss**/
.container {
  height: 100%;
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: flex-start;
  padding: 0;
  box-sizing: border-box;
  background-color: #f8f8f8;
} 

page {
  font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, Oxygen, Ubuntu, Cantarell, 'Open Sans', 'Helvetica Neue', sans-serif;
  font-size: 28rpx;
  line-height: 1.5;
  color: #333;
  background-color: #f8f8f8;
}

button {
  margin: 0;
  padding: 0;
  background-color: transparent;
  border: none;
  text-align: left;
  line-height: inherit;
  overflow: visible;
}

button::after {
  border: none;
}

input {
  outline: none;
}

.container {
  height: 100%;
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: space-between;
  padding: 200rpx 0;
  box-sizing: border-box;
}

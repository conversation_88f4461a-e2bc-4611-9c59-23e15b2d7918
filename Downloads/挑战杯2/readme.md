# 健康AI助手小程序

## 项目介绍

健康AI助手是一款基于微信小程序平台开发的健康管理应用，旨在帮助用户追踪健康数据、获取AI智能健康建议，提供全方位的健康管理服务。本应用结合了人工智能技术与健康管理理念，为用户提供个性化的健康指导。

## 功能特点

- **用户认证系统**：支持用户注册、登录，保护用户隐私和数据安全
- **健康数据追踪**：记录并展示用户的步数、消耗热量、睡眠、心率等健康指标
- **AI健康助手**：提供智能健康建议和回答用户健康相关问题
- **健康小贴士**：定期更新健康知识和生活小贴士
- **个人中心**：管理个人信息和应用设置

## 目录结构

```
├── app.js                 // 应用程序入口文件
├── app.json               // 应用程序配置文件
├── app.wxss               // 应用程序全局样式
├── images/                // 图片资源目录
│   ├── default_avatar.png // 默认头像
│   ├── ai_icon.png        // AI图标
│   ├── health_icon.png    // 健康图标
│   ├── assistant_icon.png // 助手图标
│   ├── profile_icon.png   // 个人中心图标
│   ├── ai_avatar.png      // AI头像
│   ├── user_avatar.png    // 用户头像
│   └── health_logo.png    // 健康标志
├── pages/                 // 页面目录
│   ├── assistant/         // AI助手页面
│   ├── health/            // 健康数据页面
│   ├── index/             // 首页
│   ├── login/             // 登录页面
│   ├── logs/              // 日志页面
│   ├── profile/           // 个人中心页面
│   └── register/          // 注册页面
├── utils/                 // 工具函数目录
│   └── util.js            // 工具函数
├── project.config.json    // 项目配置文件
└── sitemap.json           // 站点地图配置
```

## 页面说明

1. **登录页面**：用户登录入口，支持账号密码登录
2. **注册页面**：新用户注册，收集用户基本信息
3. **首页**：展示用户健康数据概览、AI助手消息和健康小贴士
4. **健康数据页面**：详细展示用户健康指标，包括今日、本周和本月的数据统计
5. **AI助手页面**：与AI助手进行健康咨询对话
6. **个人中心页面**：管理个人信息、设置和其他功能

## 使用说明

1. **首次使用**：
   - 打开小程序，进入登录页面
   - 新用户点击"注册"按钮，完成注册
   - 使用注册的账号密码登录系统

2. **查看健康数据**：
   - 在首页查看健康数据概览
   - 点击"健康数据"导航或卡片，进入详细健康数据页面
   - 在健康数据页面可切换查看不同时间段的数据

3. **使用AI助手**：
   - 点击首页的"AI助手"导航或卡片，进入AI助手对话页面
   - 在输入框中输入健康问题，点击发送按钮获取AI回复

4. **管理个人信息**：
   - 点击首页右上角头像或底部"我的"导航，进入个人中心
   - 在个人中心可以修改个人信息、查看历史记录等

## 开发环境

- 微信开发者工具
- 基础库版本：最低支持 2.10.4

## 注意事项

1. 首次运行项目前，请确保 `images` 目录中包含所有必要的图片资源
2. 本应用使用微信小程序提供的用户信息接口，需要用户授权才能获取头像和昵称
3. 健康数据仅作参考，不能替代专业医疗建议

## 未来计划

- 添加更多健康指标监测
- 增强AI助手的智能分析能力
- 支持数据导出和分享功能
- 增加社区互动功能

---

© 2023 健康AI助手团队 保留所有权利
        
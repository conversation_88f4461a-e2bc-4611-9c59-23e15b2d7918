<view class="container login-container">
  <view class="logo-container">
    <image class="logo" src="/images/health_logo.png" mode="aspectFit"></image>
    <text class="app-title">健康AI助手</text>
  </view>
  
  <view class="form-container">
    <view class="input-group">
      <text class="input-label">用户名</text>
      <input class="input" type="text" placeholder="请输入用户名" data-field="username" bindinput="onInputChange" />
    </view>
    
    <view class="input-group">
      <text class="input-label">密码</text>
      <input class="input" password type="text" placeholder="请输入密码" data-field="password" bindinput="onInputChange" />
    </view>
    
    <view class="error-msg" wx:if="{{errorMsg}}">{{errorMsg}}</view>
    
    <button class="login-btn" bindtap="login">登录</button>
    
    <view class="register-link" bindtap="goToRegister">还没有账号？点击注册</view>
  </view>
</view>
Page({
  data: {
    username: '',
    password: '',
    errorMsg: ''
  },

  // 输入框内容变化处理函数
  onInputChange(e) {
    const { field } = e.currentTarget.dataset;
    this.setData({
      [field]: e.detail.value
    });
  },

  // 登录处理函数
  login() {
    const { username, password } = this.data;
    
    if (!username || !password) {
      this.setData({
        errorMsg: '用户名和密码不能为空'
      });
      return;
    }
    
    // 这里可以添加实际的登录验证逻辑
    // 模拟登录成功
    wx.setStorageSync('isLoggedIn', true);
    wx.setStorageSync('username', username);
    
    // 登录成功后跳转到首页
    wx.switchTab({
      url: '/pages/index/index'
    });
  },

  // 跳转到注册页面
  goToRegister() {
    wx.navigateTo({
      url: '/pages/register/register'
    });
  }
});
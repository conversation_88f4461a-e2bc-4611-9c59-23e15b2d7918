// 云函数 getAIResponse/index.js
const cloud = require('wx-server-sdk');
const axios = require('axios');

cloud.init({
  env: cloud.DYNAMIC_CURRENT_ENV
});

exports.main = async (event, context) => {
  try {
    // 获取用户输入和历史消息
    const { query, history } = event;
    
    // 调用第三方AI API（以OpenAI为例）
    const response = await axios.post(
      'https://api.openai.com/v1/chat/completions',
      {
        model: "gpt-3.5-turbo",
        messages: [
          {role: "system", content: "你是一个专业的健康顾问，提供健康相关的建议和知识。"}, 
          ...history,
          {role: "user", content: query}
        ],
        max_tokens: 500
      },
      {
        headers: {
          'Authorization': `Bearer ${process.env.OPENAI_API_KEY}`,
          'Content-Type': 'application/json'
        }
      }
    );
    
    return {
      response: response.data.choices[0].message.content
    };
  } catch (error) {
    console.error(error);
    return {
      response: "抱歉，我暂时无法回答您的问题。请稍后再试。"
    };
  }
};
.health-container {
  padding: 30rpx;
  justify-content: flex-start;
  background-color: #f8f8f8;
}

.tab-nav {
  display: flex;
  width: 100%;
  background-color: #fff;
  border-radius: 12rpx;
  margin-bottom: 30rpx;
  overflow: hidden;
  box-shadow: 0 2rpx 10rpx rgba(0, 0, 0, 0.05);
}

.tab-item {
  flex: 1;
  text-align: center;
  padding: 24rpx 0;
  font-size: 28rpx;
  color: #666;
  position: relative;
}

.tab-item.active {
  color: #4CAF50;
  font-weight: bold;
}

.tab-item.active::after {
  content: '';
  position: absolute;
  bottom: 0;
  left: 50%;
  transform: translateX(-50%);
  width: 40rpx;
  height: 6rpx;
  background-color: #4CAF50;
  border-radius: 3rpx;
}

.tab-content {
  width: 100%;
}

.data-card {
  background-color: #fff;
  border-radius: 12rpx;
  padding: 30rpx;
  margin-bottom: 30rpx;
  box-shadow: 0 2rpx 10rpx rgba(0, 0, 0, 0.05);
}

.card-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 20rpx;
}

.card-title {
  font-size: 30rpx;
  font-weight: bold;
  color: #333;
}

.card-more {
  font-size: 24rpx;
  color: #999;
}

.card-body {
  width: 100%;
}

.main-data {
  margin-bottom: 10rpx;
}

.data-number {
  font-size: 60rpx;
  font-weight: bold;
  color: #4CAF50;
}

.data-unit {
  font-size: 28rpx;
  color: #999;
  margin-left: 10rpx;
}

.sub-data {
  font-size: 24rpx;
  color: #666;
}

.chart-container {
  display: flex;
  flex-direction: column;
  gap: 20rpx;
  padding: 20rpx 0;
}

.monthly-data-item {
  display: flex;
  justify-content: space-between;
  padding: 20rpx 0;
  border-bottom: 1rpx solid #f0f0f0;
}

.monthly-data-item:last-child {
  border-bottom: none;
}

.monthly-data-label {
  font-size: 28rpx;
  color: #333;
}

.monthly-data-value {
  font-size: 28rpx;
  font-weight: bold;
  color: #4CAF50;
}
<view class="container health-container">
  <!-- 标签页导航 -->
  <view class="tab-nav">
    <view 
      class="tab-item {{activeTab === index ? 'active' : ''}}" 
      wx:for="{{tabs}}" 
      wx:key="*this"
      data-index="{{index}}"
      bindtap="switchTab"
    >
      {{item}}
    </view>
  </view>

  <!-- 今日数据 -->
  <view class="tab-content" hidden="{{activeTab !== 0}}">
    <view class="data-card" bindtap="goToDetailData" data-type="steps">
      <view class="card-header">
        <text class="card-title">步数</text>
        <text class="card-more">详情 ></text>
      </view>
      <view class="card-body">
        <view class="main-data">
          <text class="data-number">{{dailyData.steps}}</text>
          <text class="data-unit">步</text>
        </view>
        <view class="sub-data">
          <text>距离 {{dailyData.distance}}km</text>
        </view>
      </view>
    </view>

    <view class="data-card" bindtap="goToDetailData" data-type="calories">
      <view class="card-header">
        <text class="card-title">消耗热量</text>
        <text class="card-more">详情 ></text>
      </view>
      <view class="card-body">
        <view class="main-data">
          <text class="data-number">{{dailyData.calories}}</text>
          <text class="data-unit">kcal</text>
        </view>
        <view class="sub-data">
          <text>活动时间 {{dailyData.activeTime}}分钟</text>
        </view>
      </view>
    </view>

    <view class="data-card" bindtap="goToDetailData" data-type="sleep">
      <view class="card-header">
        <text class="card-title">睡眠</text>
        <text class="card-more">详情 ></text>
      </view>
      <view class="card-body">
        <view class="main-data">
          <text class="data-number">{{dailyData.sleepHours}}</text>
          <text class="data-unit">小时</text>
        </view>
        <view class="sub-data">
          <text>深睡 3.2小时</text>
        </view>
      </view>
    </view>

    <view class="data-card" bindtap="goToDetailData" data-type="heart">
      <view class="card-header">
        <text class="card-title">心率</text>
        <text class="card-more">详情 ></text>
      </view>
      <view class="card-body">
        <view class="main-data">
          <text class="data-number">{{dailyData.heartRate}}</text>
          <text class="data-unit">bpm</text>
        </view>
        <view class="sub-data">
          <text>静息心率 65bpm</text>
        </view>
      </view>
    </view>

    <view class="data-card" bindtap="goToDetailData" data-type="water">
      <view class="card-header">
        <text class="card-title">饮水量</text>
        <text class="card-more">详情 ></text>
      </view>
      <view class="card-body">
        <view class="main-data">
          <text class="data-number">{{dailyData.waterIntake}}</text>
          <text class="data-unit">ml</text>
        </view>
        <view class="sub-data">
          <text>目标 2000ml</text>
        </view>
      </view>
    </view>
  </view>

  <!-- 本周数据 -->
  <view class="tab-content" hidden="{{activeTab !== 1}}">
    <view class="data-card">
      <view class="card-header">
        <text class="card-title">本周步数趋势</text>
      </view>
      <view class="card-body">
        <view class="chart-container">
          <!-- 这里可以添加图表组件，暂时用文本代替 -->
          <text>周一: {{weeklyData.steps[0]}}步</text>
          <text>周二: {{weeklyData.steps[1]}}步</text>
          <text>周三: {{weeklyData.steps[2]}}步</text>
          <text>周四: {{weeklyData.steps[3]}}步</text>
          <text>周五: {{weeklyData.steps[4]}}步</text>
        </view>
      </view>
    </view>

    <view class="data-card">
      <view class="card-header">
        <text class="card-title">本周热量消耗</text>
      </view>
      <view class="card-body">
        <view class="chart-container">
          <!-- 这里可以添加图表组件，暂时用文本代替 -->
          <text>周一: {{weeklyData.calories[0]}}kcal</text>
          <text>周二: {{weeklyData.calories[1]}}kcal</text>
          <text>周三: {{weeklyData.calories[2]}}kcal</text>
          <text>周四: {{weeklyData.calories[3]}}kcal</text>
          <text>周五: {{weeklyData.calories[4]}}kcal</text>
        </view>
      </view>
    </view>

    <view class="data-card">
      <view class="card-header">
        <text class="card-title">本周睡眠情况</text>
      </view>
      <view class="card-body">
        <view class="chart-container">
          <!-- 这里可以添加图表组件，暂时用文本代替 -->
          <text>周一: {{weeklyData.sleepHours[0]}}小时</text>
          <text>周二: {{weeklyData.sleepHours[1]}}小时</text>
          <text>周三: {{weeklyData.sleepHours[2]}}小时</text>
          <text>周四: {{weeklyData.sleepHours[3]}}小时</text>
          <text>周五: {{weeklyData.sleepHours[4]}}小时</text>
        </view>
      </view>
    </view>
  </view>

  <!-- 本月数据 -->
  <view class="tab-content" hidden="{{activeTab !== 2}}">
    <view class="data-card">
      <view class="card-header">
        <text class="card-title">本月健康概览</text>
      </view>
      <view class="card-body">
        <view class="monthly-data-item">
          <text class="monthly-data-label">平均每日步数</text>
          <text class="monthly-data-value">{{monthlyData.averageSteps}}步</text>
        </view>
        <view class="monthly-data-item">
          <text class="monthly-data-label">总消耗热量</text>
          <text class="monthly-data-value">{{monthlyData.totalCalories}}kcal</text>
        </view>
        <view class="monthly-data-item">
          <text class="monthly-data-label">平均睡眠时长</text>
          <text class="monthly-data-value">{{monthlyData.averageSleep}}小时</text>
        </view>
      </view>
    </view>
  </view>
</view>
<view class="container profile-container">
  <view class="user-card">
    <image class="user-avatar" src="{{userInfo.avatarUrl || '/images/default_avatar.png'}}"></image>
    <text class="user-name">{{userInfo.nickName || '用户'}}</text>
  </view>

  <view class="menu-list">
    <view class="menu-item" bindtap="goToSettings">
      <text class="menu-text">个人设置</text>
      <text class="menu-arrow">></text>
    </view>
    
    <view class="menu-item" bindtap="goToAbout">
      <text class="menu-text">关于我们</text>
      <text class="menu-arrow">></text>
    </view>
    
    <view class="menu-item logout" bindtap="logout">
      <text class="menu-text">退出登录</text>
    </view>
  </view>

  <view class="app-info">
    <text class="app-version">健康AI助手 v1.0.0</text>
  </view>
</view>
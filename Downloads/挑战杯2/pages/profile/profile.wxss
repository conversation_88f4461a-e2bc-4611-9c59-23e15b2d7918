.profile-container {
  padding: 40rpx 30rpx;
  justify-content: flex-start;
  background-color: #f8f8f8;
}

.user-card {
  width: 100%;
  display: flex;
  flex-direction: column;
  align-items: center;
  background-color: #fff;
  border-radius: 16rpx;
  padding: 60rpx 0;
  margin-bottom: 40rpx;
  box-shadow: 0 2rpx 10rpx rgba(0, 0, 0, 0.05);
}

.user-avatar {
  width: 160rpx;
  height: 160rpx;
  border-radius: 50%;
  margin-bottom: 30rpx;
  background-color: #eee;
}

.user-name {
  font-size: 36rpx;
  font-weight: bold;
  color: #333;
}

.menu-list {
  width: 100%;
  background-color: #fff;
  border-radius: 16rpx;
  overflow: hidden;
  margin-bottom: 40rpx;
  box-shadow: 0 2rpx 10rpx rgba(0, 0, 0, 0.05);
}

.menu-item {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 30rpx;
  border-bottom: 1rpx solid #f0f0f0;
}

.menu-item:last-child {
  border-bottom: none;
}

.menu-text {
  font-size: 30rpx;
  color: #333;
}

.menu-arrow {
  font-size: 30rpx;
  color: #999;
}

.logout {
  justify-content: center;
}

.logout .menu-text {
  color: #f44336;
}

.app-info {
  width: 100%;
  text-align: center;
  margin-top: 60rpx;
}

.app-version {
  font-size: 24rpx;
  color: #999;
}
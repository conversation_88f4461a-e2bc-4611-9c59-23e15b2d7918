.assistant-container {
  display: flex;
  flex-direction: column;
  height: 100vh;
  padding: 0;
  background-color: #f5f5f5;
}

.message-list {
  flex: 1;
  padding: 30rpx;
  box-sizing: border-box;
}

.message-item {
  display: flex;
  margin-bottom: 30rpx;
}

.message-avatar {
  width: 80rpx;
  height: 80rpx;
  border-radius: 50%;
  overflow: hidden;
  margin-right: 20rpx;
}

.message-avatar image {
  width: 100%;
  height: 100%;
}

.message-content {
  max-width: 70%;
  padding: 20rpx;
  border-radius: 10rpx;
  font-size: 28rpx;
  line-height: 1.5;
  word-break: break-all;
}

.ai .message-content {
  background-color: #fff;
  color: #333;
}

.user {
  flex-direction: row-reverse;
}

.user .message-avatar {
  margin-right: 0;
  margin-left: 20rpx;
}

.user .message-content {
  background-color: #4CAF50;
  color: #fff;
}

.input-area {
  display: flex;
  padding: 20rpx 30rpx;
  background-color: #fff;
  border-top: 1rpx solid #eee;
}

.message-input {
  flex: 1;
  height: 80rpx;
  background-color: #f5f5f5;
  border-radius: 40rpx;
  padding: 0 30rpx;
  font-size: 28rpx;
}

.send-btn {
  width: 120rpx;
  height: 80rpx;
  line-height: 80rpx;
  margin-left: 20rpx;
  background-color: #4CAF50;
  color: #fff;
  font-size: 28rpx;
  border-radius: 40rpx;
  padding: 0;
}
/* AI助手页面样式 - 现代化设计 */
.assistant-container {
  display: flex;
  flex-direction: column;
  height: 100vh;
  padding: 0;
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  position: relative;
}

/* 背景装饰 */
.assistant-container::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: url('data:image/svg+xml,<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 100 100"><defs><pattern id="grain" width="100" height="100" patternUnits="userSpaceOnUse"><circle cx="25" cy="25" r="1" fill="rgba(255,255,255,0.1)"/><circle cx="75" cy="75" r="1" fill="rgba(255,255,255,0.1)"/><circle cx="50" cy="10" r="0.5" fill="rgba(255,255,255,0.05)"/><circle cx="20" cy="80" r="0.5" fill="rgba(255,255,255,0.05)"/></pattern></defs><rect width="100" height="100" fill="url(%23grain)"/></svg>');
  pointer-events: none;
  z-index: 0;
}

.message-list {
  flex: 1;
  padding: 30rpx;
  box-sizing: border-box;
  position: relative;
  z-index: 1;
  overflow-y: auto;
}

.message-item {
  display: flex;
  margin-bottom: 40rpx;
  animation: messageSlideIn 0.3s ease-out;
  opacity: 0;
  animation-fill-mode: forwards;
}

@keyframes messageSlideIn {
  from {
    opacity: 0;
    transform: translateY(20rpx);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

.message-avatar {
  width: 80rpx;
  height: 80rpx;
  border-radius: 50%;
  overflow: hidden;
  margin-right: 20rpx;
  box-shadow: 0 4rpx 12rpx rgba(0, 0, 0, 0.15);
  border: 3rpx solid rgba(255, 255, 255, 0.3);
  transition: transform 0.2s ease;
}

.message-avatar:hover {
  transform: scale(1.05);
}

.message-avatar image {
  width: 100%;
  height: 100%;
  object-fit: cover;
}

.message-content {
  max-width: 75%;
  padding: 24rpx 28rpx;
  border-radius: 24rpx;
  font-size: 28rpx;
  line-height: 1.6;
  word-break: break-word;
  position: relative;
  box-shadow: 0 4rpx 20rpx rgba(0, 0, 0, 0.1);
  transition: transform 0.2s ease, box-shadow 0.2s ease;
}

.message-content:hover {
  transform: translateY(-2rpx);
  box-shadow: 0 8rpx 25rpx rgba(0, 0, 0, 0.15);
}

/* AI消息样式 */
.ai .message-content {
  background: linear-gradient(135deg, #ffffff 0%, #f8f9ff 100%);
  color: #2c3e50;
  border: 1rpx solid rgba(102, 126, 234, 0.1);
}

.ai .message-content::before {
  content: '';
  position: absolute;
  left: -12rpx;
  top: 20rpx;
  width: 0;
  height: 0;
  border-top: 12rpx solid transparent;
  border-bottom: 12rpx solid transparent;
  border-right: 12rpx solid #ffffff;
  filter: drop-shadow(-2rpx 0 4rpx rgba(0, 0, 0, 0.1));
}

/* 用户消息样式 */
.user {
  flex-direction: row-reverse;
}

.user .message-avatar {
  margin-right: 0;
  margin-left: 20rpx;
}

.user .message-content {
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  color: #ffffff;
  border: 1rpx solid rgba(255, 255, 255, 0.2);
}

.user .message-content::after {
  content: '';
  position: absolute;
  right: -12rpx;
  top: 20rpx;
  width: 0;
  height: 0;
  border-top: 12rpx solid transparent;
  border-bottom: 12rpx solid transparent;
  border-left: 12rpx solid #667eea;
  filter: drop-shadow(2rpx 0 4rpx rgba(0, 0, 0, 0.1));
}

/* 输入区域样式 */
.input-area {
  display: flex;
  padding: 24rpx 30rpx 40rpx;
  background: rgba(255, 255, 255, 0.95);
  backdrop-filter: blur(20rpx);
  border-top: 1rpx solid rgba(255, 255, 255, 0.2);
  position: relative;
  z-index: 2;
}

.input-area::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  height: 1rpx;
  background: linear-gradient(90deg, transparent, rgba(102, 126, 234, 0.3), transparent);
}

.message-input {
  flex: 1;
  height: 88rpx;
  background: rgba(248, 249, 255, 0.8);
  border: 2rpx solid rgba(102, 126, 234, 0.2);
  border-radius: 44rpx;
  padding: 0 32rpx;
  font-size: 28rpx;
  color: #2c3e50;
  transition: all 0.3s ease;
}

.message-input:focus {
  border-color: #667eea;
  background: rgba(255, 255, 255, 0.9);
  box-shadow: 0 0 0 6rpx rgba(102, 126, 234, 0.1);
}

.send-btn {
  width: 88rpx;
  height: 88rpx;
  margin-left: 20rpx;
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  color: #fff;
  font-size: 24rpx;
  border-radius: 44rpx;
  padding: 0;
  display: flex;
  align-items: center;
  justify-content: center;
  box-shadow: 0 4rpx 15rpx rgba(102, 126, 234, 0.4);
  transition: all 0.3s ease;
  position: relative;
  overflow: hidden;
}

.send-btn::before {
  content: '';
  position: absolute;
  top: 50%;
  left: 50%;
  width: 0;
  height: 0;
  background: rgba(255, 255, 255, 0.3);
  border-radius: 50%;
  transition: all 0.3s ease;
  transform: translate(-50%, -50%);
}

.send-btn:active::before {
  width: 200rpx;
  height: 200rpx;
}

.send-btn:active {
  transform: scale(0.95);
}

/* 加载动画 */
.loading-message {
  display: flex;
  align-items: center;
  margin-bottom: 40rpx;
}

.loading-dots {
  display: flex;
  align-items: center;
  padding: 24rpx 28rpx;
  background: rgba(255, 255, 255, 0.9);
  border-radius: 24rpx;
  margin-left: 100rpx;
}

.loading-dot {
  width: 8rpx;
  height: 8rpx;
  border-radius: 50%;
  background: #667eea;
  margin: 0 4rpx;
  animation: loadingBounce 1.4s infinite ease-in-out;
}

.loading-dot:nth-child(1) { animation-delay: -0.32s; }
.loading-dot:nth-child(2) { animation-delay: -0.16s; }
.loading-dot:nth-child(3) { animation-delay: 0s; }

@keyframes loadingBounce {
  0%, 80%, 100% {
    transform: scale(0.8);
    opacity: 0.5;
  }
  40% {
    transform: scale(1);
    opacity: 1;
  }
}

/* 消息时间样式 */
.message-time {
  font-size: 20rpx;
  color: rgba(0, 0, 0, 0.4);
  margin-top: 8rpx;
  text-align: right;
}

.user .message-time {
  color: rgba(255, 255, 255, 0.7);
  text-align: left;
}

/* 发送按钮禁用状态 */
.send-btn[disabled] {
  background: linear-gradient(135deg, #cccccc 0%, #999999 100%);
  box-shadow: none;
  opacity: 0.6;
}

/* 输入框禁用状态 */
.message-input[disabled] {
  background: rgba(240, 240, 240, 0.8);
  color: #999999;
}
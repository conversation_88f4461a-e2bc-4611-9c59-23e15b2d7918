<view class="container assistant-container">
  <scroll-view class="message-list" scroll-y scroll-into-view="{{scrollToView}}">
    <view class="message-item {{item.type}}" wx:for="{{messages}}" wx:key="index" id="msg-{{index}}">
      <view class="message-avatar">
        <image src="{{item.type === 'ai' ? '/images/ai_avatar.png' : '/images/user_avatar.png'}}"></image>
      </view>
      <view class="message-content">
        <text>{{item.content}}</text>
      </view>
    </view>
  </scroll-view>
  
  <view class="input-area">
    <input class="message-input" type="text" placeholder="请输入您的问题" value="{{inputValue}}" bindinput="onInputChange" confirm-type="send" bindconfirm="sendMessage" />
    <button class="send-btn" bindtap="sendMessage">发送</button>
  </view>
</view>
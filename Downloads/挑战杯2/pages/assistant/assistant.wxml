<view class="container assistant-container">
  <scroll-view class="message-list" scroll-y scroll-into-view="{{scrollToView}}" enhanced="{{true}}" show-scrollbar="{{false}}">
    <!-- 消息列表 -->
    <view class="message-item {{item.type}}" wx:for="{{messages}}" wx:key="index" id="msg-{{index}}" style="animation-delay: {{index * 0.1}}s;">
      <view class="message-avatar">
        <image src="{{item.type === 'ai' ? '/images/ai_avatar.png' : '/images/user_avatar.png'}}" mode="aspectFill"></image>
      </view>
      <view class="message-content">
        <text selectable="{{true}}">{{item.content}}</text>
        <view class="message-time" wx:if="{{item.time}}">{{item.time}}</view>
      </view>
    </view>

    <!-- 加载状态 -->
    <view class="loading-message" wx:if="{{isLoading}}">
      <view class="message-avatar">
        <image src="/images/ai_avatar.png" mode="aspectFill"></image>
      </view>
      <view class="loading-dots">
        <view class="loading-dot"></view>
        <view class="loading-dot"></view>
        <view class="loading-dot"></view>
      </view>
    </view>
  </scroll-view>

  <!-- 输入区域 -->
  <view class="input-area">
    <input
      class="message-input"
      type="text"
      placeholder="{{isLoading ? '正在思考中...' : '请输入您的健康问题'}}"
      value="{{inputValue}}"
      bindinput="onInputChange"
      confirm-type="send"
      bindconfirm="sendMessage"
      disabled="{{isLoading}}"
      maxlength="500"
    />
    <button class="send-btn" bindtap="sendMessage" disabled="{{isLoading || !inputValue.trim()}}">
      <text wx:if="{{!isLoading}}">发送</text>
      <text wx:else>...</text>
    </button>
  </view>
</view>
Page({
  data: {
    messages: [
      {
        type: 'ai',
        content: '你好！我是你的健康AI助手，有什么健康问题需要咨询吗？'
      }
    ],
    inputValue: ''
  },

  // 输入框内容变化处理函数
  onInputChange(e) {
    this.setData({
      inputValue: e.detail.value
    });
  },

  // 发送消息
  sendMessage() {
    const { inputValue, messages } = this.data;
    if (!inputValue.trim()) return;

    // 添加用户消息
    const newMessages = [...messages, {
      type: 'user',
      content: inputValue
    }];
    
    this.setData({
      messages: newMessages,
      inputValue: ''
    });

    // 模拟AI回复
    setTimeout(() => {
      let aiReply = '';
      
      // 简单的关键词匹配回复
      if (inputValue.includes('运动') || inputValue.includes('锻炼')) {
        aiReply = '建议每周进行至少150分钟中等强度有氧运动，如快走、游泳或骑自行车。同时，每周进行2次以上的肌肉强化训练。';
      } else if (inputValue.includes('饮食') || inputValue.includes('吃')) {
        aiReply = '健康饮食应当包含多样化的食物，增加蔬菜水果摄入，减少高盐高糖高脂食品。建议每天摄入至少5份蔬果。';
      } else if (inputValue.includes('睡眠')) {
        aiReply = '成年人每晚应保证7-8小时的睡眠。保持规律的作息时间，睡前避免使用电子设备，创造安静、舒适的睡眠环境有助于提高睡眠质量。';
      } else if (inputValue.includes('压力') || inputValue.includes('焦虑')) {
        aiReply = '适当的压力管理对健康很重要。可以尝试冥想、深呼吸、瑜伽等放松技巧，或者寻求专业心理咨询帮助。';
      } else {
        aiReply = '感谢您的咨询。保持均衡饮食、规律运动、充足睡眠和良好的心态是健康生活的基础。您有更具体的健康问题想了解吗？';
      }
      
      this.setData({
        messages: [...this.data.messages, {
          type: 'ai',
          content: aiReply
        }]
      });
    }, 1000);
  }
});
Page({
  data: {
    messages: [
      {
        type: 'ai',
        content: '你好！我是你的健康AI助手，基于DeepSeek AI技术，可以为您提供专业的健康建议和咨询。有什么健康问题需要咨询吗？',
        time: this.formatTime(new Date())
      }
    ],
    inputValue: '',
    isLoading: false,
    scrollToView: '',
    conversationHistory: []
  },

  onLoad() {
    // 页面加载时的初始化
    this.setData({
      scrollToView: 'msg-0'
    });
  },

  // 格式化时间
  formatTime(date) {
    const hours = date.getHours().toString().padStart(2, '0');
    const minutes = date.getMinutes().toString().padStart(2, '0');
    return `${hours}:${minutes}`;
  },

  // 输入框内容变化处理函数
  onInputChange(e) {
    this.setData({
      inputValue: e.detail.value
    });
  },

  // 发送消息
  async sendMessage() {
    const { inputValue, messages, isLoading } = this.data;
    if (!inputValue.trim() || isLoading) return;

    const userMessage = {
      type: 'user',
      content: inputValue.trim(),
      time: this.formatTime(new Date())
    };

    // 添加用户消息并清空输入框
    const newMessages = [...messages, userMessage];
    this.setData({
      messages: newMessages,
      inputValue: '',
      isLoading: true,
      scrollToView: `msg-${newMessages.length - 1}`
    });

    try {
      // 调用DeepSeek API
      const aiResponse = await this.callDeepSeekAPI(inputValue.trim());

      const aiMessage = {
        type: 'ai',
        content: aiResponse,
        time: this.formatTime(new Date())
      };

      this.setData({
        messages: [...this.data.messages, aiMessage],
        isLoading: false,
        scrollToView: `msg-${this.data.messages.length}`
      });

    } catch (error) {
      console.error('AI回复失败:', error);

      const errorMessage = {
        type: 'ai',
        content: '抱歉，我暂时无法回答您的问题。请检查网络连接后重试，或者稍后再试。',
        time: this.formatTime(new Date())
      };

      this.setData({
        messages: [...this.data.messages, errorMessage],
        isLoading: false,
        scrollToView: `msg-${this.data.messages.length}`
      });
    }
  },

  // 调用DeepSeek API
  async callDeepSeekAPI(userInput) {
    return new Promise((resolve, reject) => {
      // 构建对话历史
      const conversationHistory = this.data.messages
        .filter(msg => msg.type !== 'system')
        .slice(-10) // 只保留最近10条消息作为上下文
        .map(msg => ({
          role: msg.type === 'user' ? 'user' : 'assistant',
          content: msg.content
        }));

      // 添加系统提示
      const messages = [
        {
          role: 'system',
          content: '你是一个专业的健康AI助手，具有丰富的医学和健康知识。请为用户提供准确、实用的健康建议。注意：你的建议仅供参考，不能替代专业医疗诊断，如有严重健康问题请及时就医。请用简洁、易懂的语言回答，控制在200字以内。'
        },
        ...conversationHistory,
        {
          role: 'user',
          content: userInput
        }
      ];

      wx.request({
        url: 'https://api.deepseek.com/v1/chat/completions',
        method: 'POST',
        header: {
          'Content-Type': 'application/json',
          'Authorization': 'Bearer sk-4feada044f944f25815c4e863f2507d2'
        },
        data: {
          model: 'deepseek-chat',
          messages: messages,
          max_tokens: 500,
          temperature: 0.7,
          stream: false
        },
        success: (res) => {
          if (res.statusCode === 200 && res.data.choices && res.data.choices.length > 0) {
            const aiResponse = res.data.choices[0].message.content;
            resolve(aiResponse);
          } else {
            console.error('API响应格式错误:', res);
            reject(new Error('API响应格式错误'));
          }
        },
        fail: (error) => {
          console.error('API调用失败:', error);
          reject(error);
        }
      });
    });
  },

  // 重试发送消息
  retryLastMessage() {
    const messages = this.data.messages;
    if (messages.length >= 2) {
      const lastUserMessage = messages[messages.length - 2];
      if (lastUserMessage.type === 'user') {
        // 移除最后的错误消息
        this.setData({
          messages: messages.slice(0, -1),
          inputValue: lastUserMessage.content
        });
        // 重新发送
        this.sendMessage();
      }
    }
  }
});
.home-container {
  padding: 40rpx 30rpx;
  justify-content: flex-start;
  background-color: #f8f8f8;
}

.user-info-bar {
  width: 100%;
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 40rpx;
}

.user-greeting {
  font-size: 36rpx;
  font-weight: bold;
  color: #333;
}

.user-avatar {
  width: 80rpx;
  height: 80rpx;
  border-radius: 50%;
  overflow: hidden;
  background-color: #eee;
}

.avatar-img {
  width: 100%;
  height: 100%;
}

.section-title {
  font-size: 32rpx;
  font-weight: bold;
  color: #333;
  margin-bottom: 20rpx;
}

.health-overview, .ai-assistant, .health-tips {
  width: 100%;
  background-color: #fff;
  border-radius: 16rpx;
  padding: 30rpx;
  margin-bottom: 30rpx;
  box-shadow: 0 2rpx 10rpx rgba(0, 0, 0, 0.05);
}

.health-data-grid {
  display: flex;
  flex-wrap: wrap;
  justify-content: space-between;
}

.health-data-item {
  width: 48%;
  background-color: #f9f9f9;
  border-radius: 12rpx;
  padding: 20rpx;
  margin-bottom: 20rpx;
  text-align: center;
}

.data-value {
  font-size: 40rpx;
  font-weight: bold;
  color: #4CAF50;
  margin-bottom: 10rpx;
}

.data-unit {
  font-size: 24rpx;
  font-weight: normal;
  margin-left: 4rpx;
}

.data-title {
  font-size: 24rpx;
  color: #666;
}

.ai-message {
  display: flex;
  align-items: flex-start;
  background-color: #f0f9f0;
  border-radius: 12rpx;
  padding: 20rpx;
}

.ai-icon {
  width: 60rpx;
  height: 60rpx;
  margin-right: 20rpx;
}

.ai-text {
  font-size: 28rpx;
  color: #333;
  line-height: 1.5;
}

.tips-list {
  display: flex;
  flex-direction: column;
}

.tip-item {
  display: flex;
  margin-bottom: 16rpx;
}

.tip-dot {
  font-size: 36rpx;
  color: #4CAF50;
  margin-right: 10rpx;
  line-height: 1;
}

.tip-text {
  font-size: 28rpx;
  color: #333;
  line-height: 1.4;
}

.feature-nav {
  width: 100%;
  display: flex;
  justify-content: space-around;
  margin-top: 20rpx;
}

.nav-item {
  display: flex;
  flex-direction: column;
  align-items: center;
}

.nav-icon {
  width: 80rpx;
  height: 80rpx;
  margin-bottom: 10rpx;
}

.nav-text {
  font-size: 24rpx;
  color: #333;
}

.nickname-label {
    font-size: 28rpx;
    color: #333;
  }

.avatar-wrapper {
    width: 120rpx;
    height: 120rpx;
    border-radius: 50%;
    overflow: hidden;
    margin: 20rpx auto;
  }
  
  .avatar {
    width: 100%;
    height: 100%;
    border-radius: 50%;
  }
  
  .nickname-wrapper {
    margin: 20rpx;
    display: flex;
    flex-direction: column;
  }
  
  .nickname-label {
    font-size: 28rpx;
    color: #333;
  }
  
  .nickname-input, .safe-password {
    border: 1rpx solid #ccc;
    border-radius: 10rpx;
    padding: 10rpx;
  }

.avatar-wrapper {
    width: 120rpx;
    height: 120rpx;
    border-radius: 50%;
    overflow: hidden;
    margin: 20rpx auto;
  }
  
  .avatar {
    width: 100%;
    height: 100%;
    border-radius: 50%;
  }
  
  .nickname-wrapper {
    margin: 20rpx;
    display: flex;
    flex-direction: column;
  }
  
  .nickname-label {
    font-size: 28rpx;
    color: #333;
  }
  
  .nickname-input, .safe-password {
    border: 1rpx solid #ccc;
    border-radius: 10rpx;
    padding: 10rpx;
  }

.avatar-wrapper {
    width: 120rpx;
    height: 120rpx;
    border-radius: 50%;
    overflow: hidden;
    margin: 20rpx auto;
  }
  
  .avatar {
    width: 100%;
    height: 100%;
    border-radius: 50%;
  }
  
  .nickname-wrapper {
    margin: 20rpx;
    display: flex;
    flex-direction: column;
  }
  
  .nickname-label {
    font-size: 28rpx;
    color: #333;
  }
  
  .nickname-input, .safe-password {
    border: 1rpx solid #ccc;
    border-radius: 10rpx;
    padding: 10rpx;
  }

.avatar-wrapper {
    width: 120rpx;
    height: 120rpx;
    border-radius: 50%;
    overflow: hidden;
    margin: 20rpx auto;
  }
  
  .avatar {
    width: 100%;
    height: 100%;
    border-radius: 50%;
  }
  
  .nickname-wrapper {
    margin: 20rpx;
    display: flex;
    flex-direction: column;
  }
  
  .nickname-label {
    font-size: 28rpx;
    color: #333;
  }
  
  .nickname-input, .safe-password {
    border: 1rpx solid #ccc;
    border-radius: 10rpx;
    padding: 10rpx;
  }

.avatar-wrapper {
    width: 120rpx;
    height: 120rpx;
    border-radius: 50%;
    overflow: hidden;
    margin: 20rpx auto;
  }
  
  .avatar {
    width: 100%;
    height: 100%;
    border-radius: 50%;
  }
  
  .nickname-wrapper {
    margin: 20rpx;
    display: flex;
    flex-direction: column;
  }
  
  .nickname-label {
    font-size: 28rpx;
    color: #333;
  }
  
  .nickname-input, .safe-password {
    border: 1rpx solid #ccc;
    border-radius: 10rpx;
    padding: 10rpx;
  }

.avatar-wrapper {
    width: 120rpx;
    height: 120rpx;
    border-radius: 50%;
    overflow: hidden;
    margin: 20rpx auto;
  }
  
  .avatar {
    width: 100%;
    height: 100%;
    border-radius: 50%;
  }
  
  .nickname-wrapper {
    margin: 20rpx;
    display: flex;
    flex-direction: column;
  }
  
  .nickname-label {
    font-size: 28rpx;
    color: #333;
  }
  
  .nickname-input, .safe-password {
    border: 1rpx solid #ccc;
    border-radius: 10rpx;
    padding: 10rpx;
  }

.avatar-wrapper {
    width: 120rpx;
    height: 120rpx;
    border-radius: 50%;
    overflow: hidden;
    margin: 20rpx auto;
  }
  
  .avatar {
    width: 100%;
    height: 100%;
    border-radius: 50%;
  }
  
  .nickname-wrapper {
    margin: 20rpx;
    display: flex;
    flex-direction: column;
  }
  
  .nickname-label {
    font-size: 28rpx;
    color: #333;
  }
  
  .nickname-input, .safe-password {
    border: 1rpx solid #ccc;
    border-radius: 10rpx;
    padding: 10rpx;
  }

.avatar-wrapper {
    width: 120rpx;
    height: 120rpx;
    border-radius: 50%;
    overflow: hidden;
    margin: 20rpx auto;
  }
  
  .avatar {
    width: 100%;
    height: 100%;
    border-radius: 50%;
  }
  
  .nickname-wrapper {
    margin: 20rpx;
    display: flex;
    flex-direction: column;
  }
  
  .nickname-label {
    font-size: 28rpx;
    color: #333;
  }
  
  .nickname-input, .safe-password {
    border: 1rpx solid #ccc;
    border-radius: 10rpx;
    padding: 10rpx;
  }

.avatar-wrapper {
    width: 120rpx;
    height: 120rpx;
    border-radius: 50%;
    overflow: hidden;
    margin: 20rpx auto;
  }
  
  .avatar {
    width: 100%;
    height: 100%;
    border-radius: 50%;
  }
  
  .nickname-wrapper {
    margin: 20rpx;
    display: flex;
    flex-direction: column;
  }
  
  .nickname-label {
    font-size: 28rpx;
    color: #333;
  }
  
  .nickname-input, .safe-password {
    border: 1rpx solid #ccc;
    border-radius: 10rpx;
    padding: 10rpx;
  }

.avatar-wrapper {
    width: 120rpx;
    height: 120rpx;
    border-radius: 50%;
    overflow: hidden;
    margin: 20rpx auto;
  }
  
  .avatar {
    width: 100%;
    height: 100%;
    border-radius: 50%;
  }
  
  .nickname-wrapper {
    margin: 20rpx;
    display: flex;
    flex-direction: column;
  }
  
  .nickname-label {
    font-size: 28rpx;
    color: #333;
  }
  
  .nickname-input, .safe-password {
    border: 1rpx solid #ccc;
    border-radius: 10rpx;
    padding: 10rpx;
  }

.avatar-wrapper {
    width: 120rpx;
    height: 120rpx;
    border-radius: 50%;
    overflow: hidden;
    margin: 20rpx auto;
  }
  
  .avatar {
    width: 100%;
    height: 100%;
    border-radius: 50%;
  }
  
  .nickname-wrapper {
    margin: 20rpx;
    display: flex;
    flex-direction: column;
  }
  
  .nickname-label {
    font-size: 28rpx;
    color: #333;
  }
  
  .nickname-input, .safe-password {
    border: 1rpx solid #ccc;
    border-radius: 10rpx;
    padding: 10rpx;
  }

.avatar-wrapper {
    width: 120rpx;
    height: 120rpx;
    border-radius: 50%;
    overflow: hidden;
    margin: 20rpx auto;
  }
  
  .avatar {
    width: 100%;
    height: 100%;
    border-radius: 50%;
  }
  
  .nickname-wrapper {
    margin: 20rpx;
    display: flex;
    flex-direction: column;
  }
  
  .nickname-label {
    font-size: 28rpx;
    color: #333;
  }
  
  .nickname-input, .safe-password {
    border: 1rpx solid #ccc;
    border-radius: 10rpx;
    padding: 10rpx;
  }

.avatar-wrapper {
    width: 120rpx;
    height: 120rpx;
    border-radius: 50%;
    overflow: hidden;
    margin: 20rpx auto;
  }
  
  .avatar {
    width: 100%;
    height: 100%;
    border-radius: 50%;
  }
  
  .nickname-wrapper {
    margin: 20rpx;
    display: flex;
    flex-direction: column;
  }
  
  .nickname-label {
    font-size: 28rpx;
    color: #333;
  }
  
  .nickname-input, .safe-password {
    border: 1rpx solid #ccc;
    border-radius: 10rpx;
    padding: 10rpx;
  }

.avatar-wrapper {
    width: 120rpx;
    height: 120rpx;
    border-radius: 50%;
    overflow: hidden;
    margin: 20rpx auto;
  }
  
  .avatar {
    width: 100%;
    height: 100%;
    border-radius: 50%;
  }
  
  .nickname-wrapper {
    margin: 20rpx;
    display: flex;
    flex-direction: column;
  }
  
  .nickname-label {
    font-size: 28rpx;
    color: #333;
  }
  
  .nickname-input, .safe-password {
    border: 1rpx solid #ccc;
    border-radius: 10rpx;
    padding: 10rpx;
  }

.avatar-wrapper {
    width: 120rpx;
    height: 120rpx;
    border-radius: 50%;
    overflow: hidden;
    margin: 20rpx auto;
  }
  
  .avatar {
    width: 100%;
    height: 100%;
    border-radius: 50%;
  }
  
  .nickname-wrapper {
    margin: 20rpx;
    display: flex;
    flex-direction: column;
  }
  
  .nickname-label {
    font-size: 28rpx;
    color: #333;
  }
  
  .nickname-input, .safe-password {
    border: 1rpx solid #ccc;
    border-radius: 10rpx;
    padding: 10rpx;
  }

.avatar-wrapper {
    width: 120rpx;
    height: 120rpx;
    border-radius: 50%;
    overflow: hidden;
    margin: 20rpx auto;
  }
  
  .avatar {
    width: 100%;
    height: 100%;
    border-radius: 50%;
  }
  
  .nickname-wrapper {
    margin: 20rpx;
    display: flex;
    flex-direction: column;
  }
  
  .nickname-label {
    font-size: 28rpx;
    color: #333;
  }
  
  .nickname-input, .safe-password {
    border: 1rpx solid #ccc;
    border-radius: 10rpx;
    padding: 10rpx;
  }

.avatar-wrapper {
    width: 120rpx;
    height: 120rpx;
    border-radius: 50%;
    overflow: hidden;
    margin: 20rpx auto;
  }
  
  .avatar {
    width: 100%;
    height: 100%;
    border-radius: 50%;
  }
  
  .nickname-wrapper {
    margin: 20rpx;
    display: flex;
    flex-direction: column;
  }
  
  .nickname-label {
    font-size: 28rpx;
    color: #333;
  }
  
  .nickname-input, .safe-password {
    border: 1rpx solid #ccc;
    border-radius: 10rpx;
    padding: 10rpx;
  }

.avatar-wrapper {
    width: 120rpx;
    height: 120rpx;
    border-radius: 50%;
    overflow: hidden;
    margin: 20rpx auto;
  }
  
  .avatar {
    width: 100%;
    height: 100%;
    border-radius: 50%;
  }
  
  .nickname-wrapper {
    margin: 20rpx;
    display: flex;
    flex-direction: column;
  }
  
  .nickname-label {
    font-size: 28rpx;
    color: #333;
  }
  
  .nickname-input, .safe-password {
    border: 1rpx solid #ccc;
    border-radius: 10rpx;
    padding: 10rpx;
  }

.avatar-wrapper {
    width: 120rpx;
    height: 120rpx;
    border-radius: 50%;
    overflow: hidden;
    margin: 20rpx auto;
  }
  
  .avatar {
    width: 100%;
    height: 100%;
    border-radius: 50%;
  }
  
  .nickname-wrapper {
    margin: 20rpx;
    display: flex;
    flex-direction: column;
  }
  
  .nickname-label {
    font-size: 28rpx;
    color: #333;
  }
  
  .nickname-input, .safe-password {
    border: 1rpx solid #ccc;
    border-radius: 10rpx;
    padding: 10rpx;
  }

.avatar-wrapper {
    width: 120rpx;
    height: 120rpx;
    border-radius: 50%;
    overflow: hidden;
    margin: 20rpx auto;
  }
  
  .avatar {
    width: 100%;
    height: 100%;
    border-radius: 50%;
  }
  
  .nickname-wrapper {
    margin: 20rpx;
    display: flex;
    flex-direction: column;
  }
  
  .nickname-label {
    font-size: 28rpx;
    color: #333;
  }
  
  .nickname-input, .safe-password {
    border: 1rpx solid #ccc;
    border-radius: 10rpx;
    padding: 10rpx;
  }

.avatar-wrapper {
    width: 120rpx;
    height: 120rpx;
    border-radius: 50%;
    overflow: hidden;
    margin: 20rpx auto;
  }
  
  .avatar {
    width: 100%;
    height: 100%;
    border-radius: 50%;
  }
  
  .nickname-wrapper {
    margin: 20rpx;
    display: flex;
    flex-direction: column;
  }
  
  .nickname-label {
    font-size: 28rpx;
    color: #333;
  }
  
  .nickname-input, .safe-password {
    border: 1rpx solid #ccc;
    border-radius: 10rpx;
    padding: 10rpx;
  }

.avatar-wrapper {
    width: 120rpx;
    height: 120rpx;
    border-radius: 50%;
    overflow: hidden;
    margin: 20rpx auto;
  }
  
  .avatar {
    width: 100%;
    height: 100%;
    border-radius: 50%;
  }
  
  .nickname-wrapper {
    margin: 20rpx;
    display: flex;
    flex-direction: column;
  }
  
  .nickname-label {
    font-size: 28rpx;
    color: #333;
  }
  
  .nickname-input, .safe-password {
    border: 1rpx solid #ccc;
    border-radius: 10rpx;
    padding: 10rpx;
  }

.avatar-wrapper {
    width: 120rpx;
    height: 120rpx;
    border-radius: 50%;
    overflow: hidden;
    margin: 20rpx auto;
  }
  
  .avatar {
    width: 100%;
    height: 100%;
    border-radius: 50%;
  }
  
  .nickname-wrapper {
    margin: 20rpx;
    display: flex;
    flex-direction: column;
  }
  
  .nickname-label {
    font-size: 28rpx;
    color: #333;
  }
  
  .nickname-input, .safe-password {
    border: 1rpx solid #ccc;
    border-radius: 10rpx;
    padding: 10rpx;
  }

.avatar-wrapper {
    width: 120rpx;
    height: 120rpx;
    border-radius: 50%;
    overflow: hidden;
    margin: 20rpx auto;
  }
  
  .avatar {
    width: 100%;
    height: 100%;
    border-radius: 50%;
  }
  
  .nickname-wrapper {
    margin: 20rpx;
    display: flex;
    flex-direction: column;
  }
  
  .nickname-label {
    font-size: 28rpx;
    color: #333;
  }
  
  .nickname-input, .safe-password {
    border: 1rpx solid #ccc;
    border-radius: 10rpx;
    padding: 10rpx;
  }

.avatar-wrapper {
    width: 120rpx;
    height: 120rpx;
    border-radius: 50%;
    overflow: hidden;
    margin: 20rpx auto;
  }
  
  .avatar {
    width: 100%;
    height: 100%;
    border-radius: 50%;
  }
  
  .nickname-wrapper {
    margin: 20rpx;
    display: flex;
    flex-direction: column;
  }
  
  .nickname-label {
    font-size: 28rpx;
    color: #333;
  }
  
  .nickname-input, .safe-password {
    border: 1rpx solid #ccc;
    border-radius: 10rpx;
    padding: 10rpx;
  }

.avatar-wrapper {
    width: 120rpx;
    height: 120rpx;
    border-radius: 50%;
    overflow: hidden;
    margin: 20rpx auto;
  }
  
  .avatar {
    width: 100%;
    height: 100%;
    border-radius: 50%;
  }
  
  .nickname-wrapper {
    margin: 20rpx;
    display: flex;
    flex-direction: column;
  }
  
  .nickname-label {
    font-size: 28rpx;
    color: #333;
  }
  
  .nickname-input, .safe-password {
    border: 1rpx solid #ccc;
    border-radius: 10rpx;
    padding: 10rpx;
  }

.avatar-wrapper {
    width: 120rpx;
    height: 120rpx;
    border-radius: 50%;
    overflow: hidden;
    margin: 20rpx auto;
  }
  
  .avatar {
    width: 100%;
    height: 100%;
    border-radius: 50%;
  }
  
  .nickname-wrapper {
    margin: 20rpx;
    display: flex;
    flex-direction: column;
  }
  
  .nickname-label {
    font-size: 28rpx;
    color: #333;
  }
  
  .nickname-input, .safe-password {
    border: 1rpx solid #ccc;
    border-radius: 10rpx;
    padding: 10rpx;
  }

.avatar-wrapper {
    width: 120rpx;
    height: 120rpx;
    border-radius: 50%;
    overflow: hidden;
    margin: 20rpx auto;
  }
  
  .avatar {
    width: 100%;
    height: 100%;
    border-radius: 50%;
  }
  
  .nickname-wrapper {
    margin: 20rpx;
    display: flex;
    flex-direction: column;
  }
  
  .nickname-label {
    font-size: 28rpx;
    color: #333;
  }
  
  .nickname-input, .safe-password {
    border: 1rpx solid #ccc;
    border-radius: 10rpx;
    padding: 10rpx;
  }

.avatar-wrapper {
    width: 120rpx;
    height: 120rpx;
    border-radius: 50%;
    overflow: hidden;
    margin: 20rpx auto;
  }
  
  .avatar {
    width: 100%;
    height: 100%;
    border-radius: 50%;
  }
  
  .nickname-wrapper {
    margin: 20rpx;
    display: flex;
    flex-direction: column;
  }
  
  .nickname-label {
    font-size: 28rpx;
    color: #333;
  }
  
  .nickname-input, .safe-password {
    border: 1rpx solid #ccc;
    border-radius: 10rpx;
    padding: 10rpx;
  }

.avatar-wrapper {
    width: 120rpx;
    height: 120rpx;
    border-radius: 50%;
    overflow: hidden;
    margin: 20rpx auto;
  }
  
  .avatar {
    width: 100%;
    height: 100%;
    border-radius: 50%;
  }
  
  .nickname-wrapper {
    margin: 20rpx;
    display: flex;
    flex-direction: column;
  }
  
  .nickname-label {
    font-size: 28rpx;
    color: #333;
  }
  
  .nickname-input, .safe-password {
    border: 1rpx solid #ccc;
    border-radius: 10rpx;
    padding: 10rpx;
  }

.avatar-wrapper {
    width: 120rpx;
    height: 120rpx;
    border-radius: 50%;
    overflow: hidden;
    margin: 20rpx auto;
  }
  
  .avatar {
    width: 100%;
    height: 100%;
    border-radius: 50%;
  }
  
  .nickname-wrapper {
    margin: 20rpx;
    display: flex;
    flex-direction: column;
  }
  
  .nickname-label {
    font-size: 28rpx;
    color: #333;
  }
  
  .nickname-input, .safe-password {
    border: 1rpx solid #ccc;
    border-radius: 10rpx;
    padding: 10rpx;
  }

.avatar-wrapper {
    width: 120rpx;
    height: 120rpx;
    border-radius: 50%;
    overflow: hidden;
    margin: 20rpx auto;
  }
  
  .avatar {
    width: 100%;
    height: 100%;
    border-radius: 50%;
  }
  
  .nickname-wrapper {
    margin: 20rpx;
    display: flex;
    flex-direction: column;
  }
  
  .nickname-label {
    font-size: 28rpx;
    color: #333;
  }
  
  .nickname-input, .safe-password {
    border: 1rpx solid #ccc;
    border-radius: 10rpx;
    padding: 10rpx;
  }

.avatar-wrapper {
    width: 120rpx;
    height: 120rpx;
    border-radius: 50%;
    overflow: hidden;
    margin: 20rpx auto;
  }
  
  .avatar {
    width: 100%;
    height: 100%;
    border-radius: 50%;
  }
  
  .nickname-wrapper {
    margin: 20rpx;
    display: flex;
    flex-direction: column;
  }
  
  .nickname-label {
    font-size: 28rpx;
    color: #333;
  }
  
  .nickname-input, .safe-password {
    border: 1rpx solid #ccc;
    border-radius: 10rpx;
    padding: 10rpx;
  }

.avatar-wrapper {
    width: 120rpx;
    height: 120rpx;
    border-radius: 50%;
    overflow: hidden;
    margin: 20rpx auto;
  }
  
  .avatar {
    width: 100%;
    height: 100%;
    border-radius: 50%;
  }
  
  .nickname-wrapper {
    margin: 20rpx;
    display: flex;
    flex-direction: column;
  }
  
  .nickname-label {
    font-size: 28rpx;
    color: #333;
  }
  
  .nickname-input, .safe-password {
    border: 1rpx solid #ccc;
    border-radius: 10rpx;
    padding: 10rpx;
  }

.avatar-wrapper {
    width: 120rpx;
    height: 120rpx;
    border-radius: 50%;
    overflow: hidden;
    margin: 20rpx auto;
  }
  
  .avatar {
    width: 100%;
    height: 100%;
    border-radius: 50%;
  }
  
  .nickname-wrapper {
    margin: 20rpx;
    display: flex;
    flex-direction: column;
  }
  
  .nickname-label {
    font-size: 28rpx;
    color: #333;
  }
  
  .nickname-input, .safe-password {
    border: 1rpx solid #ccc;
    border-radius: 10rpx;
    padding: 10rpx;
  }

.avatar-wrapper {
    width: 120rpx;
    height: 120rpx;
    border-radius: 50%;
    overflow: hidden;
    margin: 20rpx auto;
  }
  
  .avatar {
    width: 100%;
    height: 100%;
    border-radius: 50%;
  }
  
  .nickname-wrapper {
    margin: 20rpx;
    display: flex;
    flex-direction: column;
  }
  
  .nickname-label {
    font-size: 28rpx;
    color: #333;
  }
  
  .nickname-input, .safe-password {
    border: 1rpx solid #ccc;
    border-radius: 10rpx;
    padding: 10rpx;
  }

.avatar-wrapper {
    width: 120rpx;
    height: 120rpx;
    border-radius: 50%;
    overflow: hidden;
    margin: 20rpx auto;
  }
  
  .avatar {
    width: 100%;
    height: 100%;
    border-radius: 50%;
  }
  
  .nickname-wrapper {
    margin: 20rpx;
    display: flex;
    flex-direction: column;
  }
  
  .nickname-label {
    font-size: 28rpx;
    color: #333;
  }
  
  .nickname-input, .safe-password {
    border: 1rpx solid #ccc;
    border-radius: 10rpx;
    padding: 10rpx;
  }

.avatar-wrapper {
    width: 120rpx;
    height: 120rpx;
    border-radius: 50%;
    overflow: hidden;
    margin: 20rpx auto;
  }
  
  .avatar {
    width: 100%;
    height: 100%;
    border-radius: 50%;
  }
  
  .nickname-wrapper {
    margin: 20rpx;
    display: flex;
    flex-direction: column;
  }
  
  .nickname-label {
    font-size: 28rpx;
    color: #333;
  }
  
  .nickname-input, .safe-password {
    border: 1rpx solid #ccc;
    border-radius: 10rpx;
    padding: 10rpx;
  }

.avatar-wrapper {
    width: 120rpx;
    height: 120rpx;
    border-radius: 50%;
    overflow: hidden;
    margin: 20rpx auto;
  }
  
  .avatar {
    width: 100%;
    height: 100%;
    border-radius: 50%;
  }
  
  .nickname-wrapper {
    margin: 20rpx;
    display: flex;
    flex-direction: column;
  }
  
  .nickname-label {
    font-size: 28rpx;
    color: #333;
  }
  
  .nickname-input, .safe-password {
    border: 1rpx solid #ccc;
    border-radius: 10rpx;
    padding: 10rpx;
  }

.avatar-wrapper {
    width: 120rpx;
    height: 120rpx;
    border-radius: 50%;
    overflow: hidden;
    margin: 20rpx auto;
  }
  
  .avatar {
    width: 100%;
    height: 100%;
    border-radius: 50%;
  }
  
  .nickname-wrapper {
    margin: 20rpx;
    display: flex;
    flex-direction: column;
  }
  
  .nickname-label {
    font-size: 28rpx;
    color: #333;
  }
  
  .nickname-input, .safe-password {
    border: 1rpx solid #ccc;
    border-radius: 10rpx;
    padding: 10rpx;
  }

.avatar-wrapper {
    width: 120rpx;
    height: 120rpx;
    border-radius: 50%;
    overflow: hidden;
    margin: 20rpx auto;
  }
  
  .avatar {
    width: 100%;
    height: 100%;
    border-radius: 50%;
  }
  
  .nickname-wrapper {
    margin: 20rpx;
    display: flex;
    flex-direction: column;
  }
  
  .nickname-label {
    font-size: 28rpx;
    color: #333;
  }
  
  .nickname-input, .safe-password {
    border: 1rpx solid #ccc;
    border-radius: 10rpx;
    padding: 10rpx;
  }

.avatar-wrapper {
    width: 120rpx;
    height: 120rpx;
    border-radius: 50%;
    overflow: hidden;
    margin: 20rpx auto;
  }
  
  .avatar {
    width: 100%;
    height: 100%;
    border-radius: 50%;
  }
  
  .nickname-wrapper {
    margin: 20rpx;
    display: flex;
    flex-direction: column;
  }
  
  .nickname-label {
    font-size: 28rpx;
    color: #333;
  }
  
  .nickname-input, .safe-password {
    border: 1rpx solid #ccc;
    border-radius: 10rpx;
    padding: 10rpx;
  }

.avatar-wrapper {
    width: 120rpx;
    height: 120rpx;
    border-radius: 50%;
    overflow: hidden;
    margin: 20rpx auto;
  }
  
  .avatar {
    width: 100%;
    height: 100%;
    border-radius: 50%;
  }
  
  .nickname-wrapper {
    margin: 20rpx;
    display: flex;
    flex-direction: column;
  }
  
  .nickname-label {
    font-size: 28rpx;
    color: #333;
  }
  
  .nickname-input, .safe-password {
    border: 1rpx solid #ccc;
    border-radius: 10rpx;
    padding: 10rpx;
  }

.avatar-wrapper {
    width: 120rpx;
    height: 120rpx;
    border-radius: 50%;
    overflow: hidden;
    margin: 20rpx auto;
  }
  
  .avatar {
    width: 100%;
    height: 100%;
    border-radius: 50%;
  }
  
  .nickname-wrapper {
    margin: 20rpx;
    display: flex;
    flex-direction: column;
  }
  
  .nickname-label {
    font-size: 28rpx;
    color: #333;
  }
  
  .nickname-input, .safe-password {
    border: 1rpx solid #ccc;
    border-radius: 10rpx;
    padding: 10rpx;
  }

.avatar-wrapper {
    width: 120rpx;
    height: 120rpx;
    border-radius: 50%;
    overflow: hidden;
    margin: 20rpx auto;
  }
  
  .avatar {
    width: 100%;
    height: 100%;
    border-radius: 50%;
  }
  
  .nickname-wrapper {
    margin: 20rpx;
    display: flex;
    flex-direction: column;
  }
  
  .nickname-label {
    font-size: 28rpx;
    color: #333;
  }
  
  .nickname-input, .safe-password {
    border: 1rpx solid #ccc;
    border-radius: 10rpx;
    padding: 10rpx;
  }

.avatar-wrapper {
    width: 120rpx;
    height: 120rpx;
    border-radius: 50%;
    overflow: hidden;
    margin: 20rpx auto;
  }
  
  .avatar {
    width: 100%;
    height: 100%;
    border-radius: 50%;
  }
  
  .nickname-wrapper {
    margin: 20rpx;
    display: flex;
    flex-direction: column;
  }
  
  .nickname-label {
    font-size: 28rpx;
    color: #333;
  }
  
  .nickname-input, .safe-password {
    border: 1rpx solid #ccc;
    border-radius: 10rpx;
    padding: 10rpx;
  }

.avatar-wrapper {
    width: 120rpx;
    height: 120rpx;
    border-radius: 50%;
    overflow: hidden;
    margin: 20rpx auto;
  }
  
  .avatar {
    width: 100%;
    height: 100%;
    border-radius: 50%;
  }
  
  .nickname-wrapper {
    margin: 20rpx;
    display: flex;
    flex-direction: column;
  }
  
  .nickname-label {
    font-size: 28rpx;
    color: #333;
  }
  
  .nickname-input, .safe-password {
    border: 1rpx solid #ccc;
    border-radius: 10rpx;
    padding: 10rpx;
  }

.avatar-wrapper {
    width: 120rpx;
    height: 120rpx;
    border-radius: 50%;
    overflow: hidden;
    margin: 20rpx auto;
  }
  
  .avatar {
    width: 100%;
    height: 100%;
    border-radius: 50%;
  }
  
  .nickname-wrapper {
    margin: 20rpx;
    display: flex;
    flex-direction: column;
  }
  
  .nickname-label {
    font-size: 28rpx;
    color: #333;
  }
  
  .nickname-input, .safe-password {
    border: 1rpx solid #ccc;
    border-radius: 10rpx;
    padding: 10rpx;
  }

.avatar-wrapper {
    width: 120rpx;
    height: 120rpx;
    border-radius: 50%;
    overflow: hidden;
    margin: 20rpx auto;
  }
  
  .avatar {
    width: 100%;
    height: 100%;
    border-radius: 50%;
  }
  
  .nickname-wrapper {
    margin: 20rpx;
    display: flex;
    flex-direction: column;
  }
  
  .nickname-label {
    font-size: 28rpx;
    color: #333;
  }
  
  .nickname-input, .safe-password {
    border: 1rpx solid #ccc;
    border-radius: 10rpx;
    padding: 10rpx;
  }

.avatar-wrapper {
    width: 120rpx;
    height: 120rpx;
    border-radius: 50%;
    overflow: hidden;
    margin: 20rpx auto;
  }
  
  .avatar {
    width: 100%;
    height: 100%;
    border-radius: 50%;
  }
  
  .nickname-wrapper {
    margin: 20rpx;
    display: flex;
    flex-direction: column;
  }
  
  .nickname-label {
    font-size: 28rpx;
    color: #333;
  }
  
  .nickname-input, .safe-password {
    border: 1rpx solid #ccc;
    border-radius: 10rpx;
    padding: 10rpx;
  }

.avatar-wrapper {
    width: 120rpx;
    height: 120rpx;
    border-radius: 50%;
    overflow: hidden;
    margin: 20rpx auto;
  }
  
  .avatar {
    width: 100%;
    height: 100%;
    border-radius: 50%;
  }
  
  .nickname-wrapper {
    margin: 20rpx;
    display: flex;
    flex-direction: column;
  }
  
  .nickname-label {
    font-size: 28rpx;
    color: #333;
  }
  
  .nickname-input, .safe-password {
    border: 1rpx solid #ccc;
    border-radius: 10rpx;
    padding: 10rpx;
  }

.avatar-wrapper {
    width: 120rpx;
    height: 120rpx;
    border-radius: 50%;
    overflow: hidden;
    margin: 20rpx auto;
  }
  
  .avatar {
    width: 100%;
    height: 100%;
    border-radius: 50%;
  }
  
  .nickname-wrapper {
    margin: 20rpx;
    display: flex;
    flex-direction: column;
  }
  
  .nickname-label {
    font-size: 28rpx;
    color: #333;
  }
  
  .nickname-input, .safe-password {
    border: 1rpx solid #ccc;
    border-radius: 10rpx;
    padding: 10rpx;
  }

.avatar-wrapper {
    width: 120rpx;
    height: 120rpx;
    border-radius: 50%;
    overflow: hidden;
    margin: 20rpx auto;
  }
  
  .avatar {
    width: 100%;
    height: 100%;
    border-radius: 50%;
  }
  
  .nickname-wrapper {
    margin: 20rpx;
    display: flex;
    flex-direction: column;
  }
  
  .nickname-label {
    font-size: 28rpx;
    color: #333;
  }
  
  .nickname-input, .safe-password {
    border: 1rpx solid #ccc;
    border-radius: 10rpx;
    padding: 10rpx;
  }

.avatar-wrapper {
    width: 120rpx;
    height: 120rpx;
    border-radius: 50%;
    overflow: hidden;
    margin: 20rpx auto;
  }
  
  .avatar {
    width: 100%;
    height: 100%;
    border-radius: 50%;
  }
  
  .nickname-wrapper {
    margin: 20rpx;
    display: flex;
    flex-direction: column;
  }
  
  .nickname-label {
    font-size: 28rpx;
    color: #333;
  }
  
  .nickname-input, .safe-password {
    border: 1rpx solid #ccc;
    border-radius: 10rpx;
    padding: 10rpx;
  }

.avatar-wrapper {
    width: 120rpx;
    height: 120rpx;
    border-radius: 50%;
    overflow: hidden;
    margin: 20rpx auto;
  }
  
  .avatar {
    width: 100%;
    height: 100%;
    border-radius: 50%;
  }
  
  .nickname-wrapper {
    margin: 20rpx;
    display: flex;
    flex-direction: column;
  }
  
  .nickname-label {
    font-size: 28rpx;
    color: #333;
  }
  
  .nickname-input, .safe-password {
    border: 1rpx solid #ccc;
    border-radius: 10rpx;
    padding: 10rpx;
  }

.avatar-wrapper {
    width: 120rpx;
    height: 120rpx;
    border-radius: 50%;
    overflow: hidden;
    margin: 20rpx auto;
  }
  
  .avatar {
    width: 100%;
    height: 100%;
    border-radius: 50%;
  }
  
  .nickname-wrapper {
    margin: 20rpx;
    display: flex;
    flex-direction: column;
  }
  
  .nickname-label {
    font-size: 28rpx;
    color: #333;
  }
  
  .nickname-input, .safe-password {
    border: 1rpx solid #ccc;
    border-radius: 10rpx;
    padding: 10rpx;
  }

.avatar-wrapper {
    width: 120rpx;
    height: 120rpx;
    border-radius: 50%;
    overflow: hidden;
    margin: 20rpx auto;
  }
  
  .avatar {
    width: 100%;
    height: 100%;
    border-radius: 50%;
  }
  
  .nickname-wrapper {
    margin: 20rpx;
    display: flex;
    flex-direction: column;
  }
  
  .nickname-label {
    font-size: 28rpx;
    color: #333;
  }
  
  .nickname-input, .safe-password {
    border: 1rpx solid #ccc;
    border-radius: 10rpx;
    padding: 10rpx;
  }

.avatar-wrapper {
    width: 120rpx;
    height: 120rpx;
    border-radius: 50%;
    overflow: hidden;
    margin: 20rpx auto;
  }
  
  .avatar {
    width: 100%;
    height: 100%;
    border-radius: 50%;
  }
  
  .nickname-wrapper {
    margin: 20rpx;
    display: flex;
    flex-direction: column;
  }
  
  .nickname-label {
    font-size: 28rpx;
    color: #333;
  }
  
  .nickname-input, .safe-password {
    border: 1rpx solid #ccc;
    border-radius: 10rpx;
    padding: 10rpx;
  }

.avatar-wrapper {
    width: 120rpx;
    height: 120rpx;
    border-radius: 50%;
    overflow: hidden;
    margin: 20rpx auto;
  }
  
  .avatar {
    width: 100%;
    height: 100%;
    border-radius: 50%;
  }
  
  .nickname-wrapper {
    margin: 20rpx;
    display: flex;
    flex-direction: column;
  }
  
  .nickname-label {
    font-size: 28rpx;
    color: #333;
  }
  
  .nickname-input, .safe-password {
    border: 1rpx solid #ccc;
    border-radius: 10rpx;
    padding: 10rpx;
  }

.avatar-wrapper {
    width: 120rpx;
    height: 120rpx;
    border-radius: 50%;
    overflow: hidden;
    margin: 20rpx auto;
  }
  
  .avatar {
    width: 100%;
    height: 100%;
    border-radius: 50%;
  }
  
  .nickname-wrapper {
    margin: 20rpx;
    display: flex;
    flex-direction: column;
  }
  
  .nickname-label {
    font-size: 28rpx;
    color: #333;
  }
  
  .nickname-input, .safe-password {
    border: 1rpx solid #ccc;
    border-radius: 10rpx;
    padding: 10rpx;
  }

.avatar-wrapper {
    width: 120rpx;
    height: 120rpx;
    border-radius: 50%;
    overflow: hidden;
    margin: 20rpx auto;
  }
  
  .avatar {
    width: 100%;
    height: 100%;
    border-radius: 50%;
  }
  
  .nickname-wrapper {
    margin: 20rpx;
    display: flex;
    flex-direction: column;
  }
  
  .nickname-label {
    font-size: 28rpx;
    color: #333;
  }
  
  .nickname-input, .safe-password {
    border: 1rpx solid #ccc;
    border-radius: 10rpx;
    padding: 10rpx;
  }

.avatar-wrapper {
    width: 120rpx;
    height: 120rpx;
    border-radius: 50%;
    overflow: hidden;
    margin: 20rpx auto;
  }
  
  .avatar {
    width: 100%;
    height: 100%;
    border-radius: 50%;
  }
  
  .nickname-wrapper {
    margin: 20rpx;
    display: flex;
    flex-direction: column;
  }
  
  .nickname-label {
    font-size: 28rpx;
    color: #333;
  }
  
  .nickname-input, .safe-password {
    border: 1rpx solid #ccc;
    border-radius: 10rpx;
    padding: 10rpx;
  }

.avatar-wrapper {
    width: 120rpx;
    height: 120rpx;
    border-radius: 50%;
    overflow: hidden;
    margin: 20rpx auto;
  }
  
  .avatar {
    width: 100%;
    height: 100%;
    border-radius: 50%;
  }
  
  .nickname-wrapper {
    margin: 20rpx;
    display: flex;
    flex-direction: column;
  }
  
  .nickname-label {
    font-size: 28rpx;
    color: #333;
  }
  
  .nickname-input, .safe-password {
    border: 1rpx solid #ccc;
    border-radius: 10rpx;
    padding: 10rpx;
  }

.avatar-wrapper {
    width: 120rpx;
    height: 120rpx;
    border-radius: 50%;
    overflow: hidden;
    margin: 20rpx auto;
  }
  
  .avatar {
    width: 100%;
    height: 100%;
    border-radius: 50%;
  }
  
  .nickname-wrapper {
    margin: 20rpx;
    display: flex;
    flex-direction: column;
  }
  
  .nickname-label {
    font-size: 28rpx;
    color: #333;
  }
  
  .nickname-input, .safe-password {
    border: 1rpx solid #ccc;
    border-radius: 10rpx;
    padding: 10rpx;
  }

.avatar-wrapper {
    width: 120rpx;
    height: 120rpx;
    border-radius: 50%;
    overflow: hidden;
    margin: 20rpx auto;
  }
  
  .avatar {
    width: 100%;
    height: 100%;
    border-radius: 50%;
  }
  
  .nickname-wrapper {
    margin: 20rpx;
    display: flex;
    flex-direction: column;
  }
  
  .nickname-label {
    font-size: 28rpx;
    color: #333;
  }
  
  .nickname-input, .safe-password {
    border: 1rpx solid #ccc;
    border-radius: 10rpx;
    padding: 10rpx;
  }

.avatar-wrapper {
    width: 120rpx;
    height: 120rpx;
    border-radius: 50%;
    overflow: hidden;
    margin: 20rpx auto;
  }
  
  .avatar {
    width: 100%;
    height: 100%;
    border-radius: 50%;
  }
  
  .nickname-wrapper {
    margin: 20rpx;
    display: flex;
    flex-direction: column;
  }
  
  .nickname-label {
    font-size: 28rpx;
    color: #333;
  }
  
  .nickname-input, .safe-password {
    border: 1rpx solid #ccc;
    border-radius: 10rpx;
    padding: 10rpx;
  }

.avatar-wrapper {
    width: 120rpx;
    height: 120rpx;
    border-radius: 50%;
    overflow: hidden;
    margin: 20rpx auto;
  }
  
  .avatar {
    width: 100%;
    height: 100%;
    border-radius: 50%;
  }
  
  .nickname-wrapper {
    margin: 20rpx;
    display: flex;
    flex-direction: column;
  }
  
  .nickname-label {
    font-size: 28rpx;
    color: #333;
  }
  
  .nickname-input, .safe-password {
    border: 1rpx solid #ccc;
    border-radius: 10rpx;
    padding: 10rpx;
  }

.avatar-wrapper {
    width: 120rpx;
    height: 120rpx;
    border-radius: 50%;
    overflow: hidden;
    margin: 20rpx auto;
  }
  
  .avatar {
    width: 100%;
    height: 100%;
    border-radius: 50%;
  }
  
  .nickname-wrapper {
    margin: 20rpx;
    display: flex;
    flex-direction: column;
  }
  
  .nickname-label {
    font-size: 28rpx;
    color: #333;
  }
  
  .nickname-input, .safe-password {
    border: 1rpx solid #ccc;
    border-radius: 10rpx;
    padding: 10rpx;
  }

.avatar-wrapper {
    width: 120rpx;
    height: 120rpx;
    border-radius: 50%;
    overflow: hidden;
    margin: 20rpx auto;
  }
  
  .avatar {
    width: 100%;
    height: 100%;
    border-radius: 50%;
  }
  
  .nickname-wrapper {
    margin: 20rpx;
    display: flex;
    flex-direction: column;
  }
  
  .nickname-label {
    font-size: 28rpx;
    color: #333;
  }
  
  .nickname-input, .safe-password {
    border: 1rpx solid #ccc;
    border-radius: 10rpx;
    padding: 10rpx;
  }

.avatar-wrapper {
    width: 120rpx;
    height: 120rpx;
    border-radius: 50%;
    overflow: hidden;
    margin: 20rpx auto;
  }
  
  .avatar {
    width: 100%;
    height: 100%;
    border-radius: 50%;
  }
  
  .nickname-wrapper {
    margin: 20rpx;
    display: flex;
    flex-direction: column;
  }
  
  .nickname-label {
    font-size: 28rpx;
    color: #333;
  }
  
  .nickname-input, .safe-password {
    border: 1rpx solid #ccc;
    border-radius: 10rpx;
    padding: 10rpx;
  }

.avatar-wrapper {
    width: 120rpx;
    height: 120rpx;
    border-radius: 50%;
    overflow: hidden;
    margin: 20rpx auto;
  }
  
  .avatar {
    width: 100%;
    height: 100%;
    border-radius: 50%;
  }
  
  .nickname-wrapper {
    margin: 20rpx;
    display: flex;
    flex-direction: column;
  }
  
  .nickname-label {
    font-size: 28rpx;
    color: #333;
  }
  
  .nickname-input, .safe-password {
    border: 1rpx solid #ccc;
    border-radius: 10rpx;
    padding: 10rpx;
  }

.avatar-wrapper {
    width: 120rpx;
    height: 120rpx;
    border-radius: 50%;
    overflow: hidden;
    margin: 20rpx auto;
  }
  
  .avatar {
    width: 100%;
    height: 100%;
    border-radius: 50%;
  }
  
  .nickname-wrapper {
    margin: 20rpx;
    display: flex;
    flex-direction: column;
  }
  
  .nickname-label {
    font-size: 28rpx;
    color: #333;
  }
  
  .nickname-input, .safe-password {
    border: 1rpx solid #ccc;
    border-radius: 10rpx;
    padding: 10rpx;
  }

.avatar-wrapper {
    width: 120rpx;
    height: 120rpx;
    border-radius: 50%;
    overflow: hidden;
    margin: 20rpx auto;
  }
  
  .avatar {
    width: 100%;
    height: 100%;
    border-radius: 50%;
  }
  
  .nickname-wrapper {
    margin: 20rpx;
    display: flex;
    flex-direction: column;
  }
  
  .nickname-label {
    font-size: 28rpx;
    color: #333;
  }
  
  .nickname-input, .safe-password {
    border: 1rpx solid #ccc;
    border-radius: 10rpx;
    padding: 10rpx;
  }

.avatar-wrapper {
    width: 120rpx;
    height: 120rpx;
    border-radius: 50%;
    overflow: hidden;
    margin: 20rpx auto;
  }
  
  .avatar {
    width: 100%;
    height: 100%;
    border-radius: 50%;
  }
  
  .nickname-wrapper {
    margin: 20rpx;
    display: flex;
    flex-direction: column;
  }
  
  .nickname-label {
    font-size: 28rpx;
    color: #333;
  }
  
  .nickname-input, .safe-password {
    border: 1rpx solid #ccc;
    border-radius: 10rpx;
    padding: 10rpx;
  }

.avatar-wrapper {
    width: 120rpx;
    height: 120rpx;
    border-radius: 50%;
    overflow: hidden;
    margin: 20rpx auto;
  }
  
  .avatar {
    width: 100%;
    height: 100%;
    border-radius: 50%;
  }
  
  .nickname-wrapper {
    margin: 20rpx;
    display: flex;
    flex-direction: column;
  }
  
  .nickname-label {
    font-size: 28rpx;
    color: #333;
  }
  
  .nickname-input, .safe-password {
    border: 1rpx solid #ccc;
    border-radius: 10rpx;
    padding: 10rpx;
  }

.avatar-wrapper {
    width: 120rpx;
    height: 120rpx;
    border-radius: 50%;
    overflow: hidden;
    margin: 20rpx auto;
  }
  
  .avatar {
    width: 100%;
    height: 100%;
    border-radius: 50%;
  }
  
  .nickname-wrapper {
    margin: 20rpx;
    display: flex;
    flex-direction: column;
  }
  
  .nickname-label {
    font-size: 28rpx;
    color: #333;
  }
  
  .nickname-input, .safe-password {
    border: 1rpx solid #ccc;
    border-radius: 10rpx;
    padding: 10rpx;
  }

.avatar-wrapper {
    width: 120rpx;
    height: 120rpx;
    border-radius: 50%;
    overflow: hidden;
    margin: 20rpx auto;
  }
  
  .avatar {
    width: 100%;
    height: 100%;
    border-radius: 50%;
  }
  
  .nickname-wrapper {
    margin: 20rpx;
    display: flex;
    flex-direction: column;
  }
  
  .nickname-label {
    font-size: 28rpx;
    color: #333;
  }
  
  .nickname-input, .safe-password {
    border: 1rpx solid #ccc;
    border-radius: 10rpx;
    padding: 10rpx;
  }

.avatar-wrapper {
    width: 120rpx;
    height: 120rpx;
    border-radius: 50%;
    overflow: hidden;
    margin: 20rpx auto;
  }
  
  .avatar {
    width: 100%;
    height: 100%;
    border-radius: 50%;
  }
  
  .nickname-wrapper {
    margin: 20rpx;
    display: flex;
    flex-direction: column;
  }
  
  .nickname-label {
    font-size: 28rpx;
    color: #333;
  }
  
  .nickname-input, .safe-password {
    border: 1rpx solid #ccc;
    border-radius: 10rpx;
    padding: 10rpx;
  }

.avatar-wrapper {
    width: 120rpx;
    height: 120rpx;
    border-radius: 50%;
    overflow: hidden;
    margin: 20rpx auto;
  }
  
  .avatar {
    width: 100%;
    height: 100%;
    border-radius: 50%;
  }
  
  .nickname-wrapper {
    margin: 20rpx;
    display: flex;
    flex-direction: column;
  }
  
  .nickname-label {
    font-size: 28rpx;
    color: #333;
  }
  
  .nickname-input, .safe-password {
    border: 1rpx solid #ccc;
    border-radius: 10rpx;
    padding: 10rpx;
  }

.avatar-wrapper {
    width: 120rpx;
    height: 120rpx;
    border-radius: 50%;
    overflow: hidden;
    margin: 20rpx auto;
  }
  
  .avatar {
    width: 100%;
    height: 100%;
    border-radius: 50%;
  }
  
  .nickname-wrapper {
    margin: 20rpx;
    display: flex;
    flex-direction: column;
  }
  
  .nickname-label {
    font-size: 28rpx;
    color: #333;
  }
  
  .nickname-input, .safe-password {
    border: 1rpx solid #ccc;
    border-radius: 10rpx;
    padding: 10rpx;
  }

.avatar-wrapper {
    width: 120rpx;
    height: 120rpx;
    border-radius: 50%;
    overflow: hidden;
    margin: 20rpx auto;
  }
  
  .avatar {
    width: 100%;
    height: 100%;
    border-radius: 50%;
  }
  
  .nickname-wrapper {
    margin: 20rpx;
    display: flex;
    flex-direction: column;
  }
  
  .nickname-label {
    font-size: 28rpx;
    color: #333;
  }
  
  .nickname-input, .safe-password {
    border: 1rpx solid #ccc;
    border-radius: 10rpx;
    padding: 10rpx;
  }

.avatar-wrapper {
    width: 120rpx;
    height: 120rpx;
    border-radius: 50%;
    overflow: hidden;
    margin: 20rpx auto;
  }
  
  .avatar {
    width: 100%;
    height: 100%;
    border-radius: 50%;
  }
  
  .nickname-wrapper {
    margin: 20rpx;
    display: flex;
    flex-direction: column;
  }
  
  .nickname-label {
    font-size: 28rpx;
    color: #333;
  }
  
  .nickname-input, .safe-password {
    border: 1rpx solid #ccc;
    border-radius: 10rpx;
    padding: 10rpx;
  }

.avatar-wrapper {
    width: 120rpx;
    height: 120rpx;
    border-radius: 50%;
    overflow: hidden;
    margin: 20rpx auto;
  }
  
  .avatar {
    width: 100%;
    height: 100%;
    border-radius: 50%;
  }
  
  .nickname-wrapper {
    margin: 20rpx;
    display: flex;
    flex-direction: column;
  }
  
  .nickname-label {
    font-size: 28rpx;
    color: #333;
  }
  
  .nickname-input, .safe-password {
    border: 1rpx solid #ccc;
    border-radius: 10rpx;
    padding: 10rpx;
  }

.avatar-wrapper {
    width: 120rpx;
    height: 120rpx;
    border-radius: 50%;
    overflow: hidden;
    margin: 20rpx auto;
  }
  
  .avatar {
    width: 100%;
    height: 100%;
    border-radius: 50%;
  }
  
  .nickname-wrapper {
    margin: 20rpx;
    display: flex;
    flex-direction: column;
  }
  
  .nickname-label {
    font-size: 28rpx;
    color: #333;
  }
  
  .nickname-input, .safe-password {
    border: 1rpx solid #ccc;
    border-radius: 10rpx;
    padding: 10rpx;
  }

.avatar-wrapper {
    width: 120rpx;
    height: 120rpx;
    border-radius: 50%;
    overflow: hidden;
    margin: 20rpx auto;
  }
  
  .avatar {
    width: 100%;
    height: 100%;
    border-radius: 50%;
  }
  
  .nickname-wrapper {
    margin: 20rpx;
    display: flex;
    flex-direction: column;
  }
  
  .nickname-label {
    font-size: 28rpx;
}
Page({
  data: {
    userInfo: {},
    hasUserInfo: false,
    healthData: [
      { title: '今日步数', value: '6,248', unit: '步' },
      { title: '消耗热量', value: '325', unit: 'kcal' },
      { title: '运动时长', value: '45', unit: '分钟' },
      { title: '睡眠时长', value: '7.5', unit: '小时' }
    ],
    healthTips: [
      '每天保持6000-10000步的行走有助于心血管健康',
      '建议每天饮水量在1500-2000ml',
      '成年人每晚应保证7-8小时的睡眠时间'
    ],
    aiAssistantMsg: '根据您最近的数据，建议增加有氧运动时间，每周至少150分钟中等强度运动'
  },

  onLoad() {
    // 检查登录状态
    const isLoggedIn = wx.getStorageSync('isLoggedIn');
    if (!isLoggedIn) {
      wx.redirectTo({
        url: '/pages/login/login'
      });
      return;
    }
    
    // 获取用户信息
    const username = wx.getStorageSync('username');
    if (username) {
      this.setData({
        hasUserInfo: true,
        'userInfo.nickName': username
      });
    }
  },

  // 跳转到健康数据详情页
  goToHealthDetail() {
    wx.navigateTo({
      url: '/pages/health/health'
    });
  },

  // 跳转到AI助手页面
  goToAiAssistant() {
    wx.navigateTo({
      url: '/pages/assistant/assistant'
    });
  },

  // 跳转到个人中心
  goToProfile() {
    wx.navigateTo({
      url: '/pages/profile/profile'
    });
  }
});
  
<view class="container home-container">
  <!-- 用户信息区域 -->
  <view class="user-info-bar">
    <view class="user-greeting">
      <text>你好，{{userInfo.nickName || '用户'}}</text>
    </view>
    <view class="user-avatar" bindtap="goToProfile">
      <image class="avatar-img" src="{{userInfo.avatarUrl || '/images/default_avatar.png'}}"></image>
    </view>
  </view>

  <!-- 健康数据概览 -->
  <view class="health-overview" bindtap="goToHealthDetail">
    <view class="section-title">健康数据</view>
    <view class="health-data-grid">
      <view class="health-data-item" wx:for="{{healthData}}" wx:key="title">
        <view class="data-value">{{item.value}}<text class="data-unit">{{item.unit}}</text></view>
        <view class="data-title">{{item.title}}</view>
      </view>
    </view>
  </view>

  <!-- AI健康助手 -->
  <view class="ai-assistant" bindtap="goToAiAssistant">
    <view class="section-title">AI健康助手</view>
    <view class="ai-message">
      <image class="ai-icon" src="/images/ai_icon.png"></image>
      <text class="ai-text">{{aiAssistantMsg}}</text>
    </view>
  </view>

  <!-- 健康小贴士 -->
  <view class="health-tips">
    <view class="section-title">健康小贴士</view>
    <view class="tips-list">
      <view class="tip-item" wx:for="{{healthTips}}" wx:key="*this">
        <text class="tip-dot">•</text>
        <text class="tip-text">{{item}}</text>
      </view>
    </view>
  </view>

  <!-- 功能导航 -->
  <view class="feature-nav">
    <view class="nav-item" bindtap="goToHealthDetail">
      <image class="nav-icon" src="/images/health_icon.png"></image>
      <text class="nav-text">健康数据</text>
    </view>
    <view class="nav-item" bindtap="goToAiAssistant">
      <image class="nav-icon" src="/images/assistant_icon.png"></image>
      <text class="nav-text">AI助手</text>
    </view>
    <view class="nav-item" bindtap="goToProfile">
      <image class="nav-icon" src="/images/profile_icon.png"></image>
      <text class="nav-text">个人中心</text>
    </view>
  </view>
</view>

<scroll-view class="scrollarea" scroll-y type="list">
  <view class="container">
    <view class="userinfo">
      <block wx:if="{{canIUseNicknameComp && !hasUserInfo}}">
        <button class="avatar-wrapper" open-type="chooseAvatar" bind:chooseavatar="onChooseAvatar">
          <image class="avatar" src="{{userInfo.avatarUrl}}"></image>
        </button>
        <view class="nickname-wrapper">
          <text class="nickname-label">昵称</text>
          <input type="nickname" class="nickname-input" placeholder="请输入昵称" bind:change="onInputChange" />
        </view>
        <view class="nickname-wrapper">
          <text class="nickname-label">密码</text>
          <input type="safe-password" class="safe-password" placeholder="请输入密码" bind:change="onInputChange" />
        </view>
      </block>
      <block wx:elif="{{!hasUserInfo}}">
        <button wx:if="{{canIUseGetUserProfile}}" bindtap="getUserProfile"> 获取头像昵称 </button>
        <view wx:else> 请使用2.10.4及以上版本基础库 </view>
      </block>
      <block wx:else>
        <image bindtap="bindViewTap" class="userinfo-avatar" src="{{userInfo.avatarUrl}}" mode="cover"></image>
        <text class="userinfo-nickname">{{userInfo.nickName}}</text>
      </block>
    </view>
    <view class="usermotto">
      <text class="user-motto">{{motto}}</text>
    </view>
  </view>
</scroll-view>

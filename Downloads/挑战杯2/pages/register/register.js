Page({
  data: {
    username: '',
    password: '',
    confirmPassword: '',
    errorMsg: ''
  },

  onInputChange(e) {
    const { field } = e.currentTarget.dataset;
    this.setData({
      [field]: e.detail.value
    });
  },

  register() {
    const { username, password, confirmPassword } = this.data;
    
    if (!username || !password || !confirmPassword) {
      this.setData({
        errorMsg: '请填写完整信息'
      });
      return;
    }
    
    if (password !== confirmPassword) {
      this.setData({
        errorMsg: '两次输入的密码不一致'
      });
      return;
    }
    
    // 这里可以添加实际的注册逻辑
    // 模拟注册成功
    wx.showToast({
      title: '注册成功',
      icon: 'success',
      duration: 2000,
      success: () => {
        // 注册成功后返回登录页
        setTimeout(() => {
          wx.navigateBack();
        }, 2000);
      }
    });
  },

  goToLogin() {
    wx.navigateBack();
  }
});
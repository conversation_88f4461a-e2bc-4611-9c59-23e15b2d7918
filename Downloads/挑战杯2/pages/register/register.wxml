<view class="container register-container">
  <view class="form-title">用户注册</view>
  
  <view class="form-container">
    <view class="input-group">
      <text class="input-label">用户名</text>
      <input class="input" type="text" placeholder="请输入用户名" data-field="username" bindinput="onInputChange" />
    </view>
    
    <view class="input-group">
      <text class="input-label">密码</text>
      <input class="input" password type="text" placeholder="请输入密码" data-field="password" bindinput="onInputChange" />
    </view>
    
    <view class="input-group">
      <text class="input-label">确认密码</text>
      <input class="input" password type="text" placeholder="请再次输入密码" data-field="confirmPassword" bindinput="onInputChange" />
    </view>
    
    <view class="error-msg" wx:if="{{errorMsg}}">{{errorMsg}}</view>
    
    <button class="register-btn" bindtap="register">注册</button>
    
    <view class="login-link" bindtap="goToLogin">已有账号？返回登录</view>
  </view>
</view>
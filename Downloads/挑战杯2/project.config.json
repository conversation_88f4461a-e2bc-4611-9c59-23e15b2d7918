{"compileType": "miniprogram", "libVersion": "3.8.10", "packOptions": {"ignore": [], "include": []}, "setting": {"coverView": true, "es6": true, "postcss": true, "minified": true, "enhance": true, "showShadowRootInWxmlPanel": true, "packNpmRelationList": [], "babelSetting": {"ignore": [], "disablePlugins": [], "outputPath": ""}, "compileWorklet": false, "uglifyFileName": false, "uploadWithSourceMap": true, "packNpmManually": false, "minifyWXSS": true, "minifyWXML": true, "localPlugins": false, "condition": false, "swc": false, "disableSWC": true, "disableUseStrict": false, "useCompilerPlugins": false}, "condition": {}, "editorSetting": {"tabIndent": "auto", "tabSize": 2}, "appid": "wxfd13b6ecb6543df3", "simulatorPluginLibVersion": {}}